

-- DPD delivery methods
INSERT INTO `delivery_method` (`id`, `externalId`, `deliveryMethodUniqueIdentifier`, `name`, `desc`, `pageText`, `tooltip`, `sort`, `public`, `isRecommended`, `pageShow`, `isGift`, `calculateFree`, `deliveryDayFrom`, `deliveryDayTo`, `deliveryHourByStock`, `mutationId`, `vats`, `customFieldsJson`) VALUES
	(11, NULL, 'DPD', 'Kurýr DPD', '', '<p><span>Zásilky odeslané přes DPD dorazí adresátovi do druhého dne po odeslání. DPD nabízí možnost dodání na adresu, případně do jednoho z výdejních míst (Pickup). O expedici objednávky Vás informuje DPD prostřednictvím e-mailové a SMS zprávy, je proto zkontrolovat správnost kontaktní<PERSON>. Stejným způsobem budete informování o doručování zásilky. Na výdejním místě na Vás zásilka počká až 7 dní. Pokud nebude zásilka vyzvednuta v uvedené lhůtě, nebo ji odmítnete převzít, DPD ji vrací zpět na náš sklad. V košíku najdete i možnost doručení na Slovensko.</span></p>', NULL, 2, 1, 0, 1, 1, 1, 1, 2, '{"shop":"14:00","supplier_store":"14:00"}', 1, '{"1":"standard"}', '{"deliveryPayment":[{"icon":["527107"]}]}');
INSERT INTO `delivery_method` (`id`, `externalId`, `deliveryMethodUniqueIdentifier`, `name`, `desc`, `pageText`, `tooltip`, `sort`, `public`, `isRecommended`, `pageShow`, `isGift`, `calculateFree`, `deliveryDayFrom`, `deliveryDayTo`, `deliveryHourByStock`, `mutationId`, `vats`, `customFieldsJson`) VALUES
	(12, NULL, 'DPDPickup', 'DPD výdejní místa', '', '<p><span>DPD Pickup nabízí možnost vyzvednutí zásilky na jednom z mnoha výdejních míst po celé České republice. Zásilka je obvykle připravena k vyzvednutí následující pracovní den po odeslání. O připravenosti zásilky budete informováni prostřednictvím SMS a e-mailu. Zásilka na výdejním místě čeká až 7 dní. Tato služba je ideální pro ty, kteří nejsou během dne doma nebo preferují vyzvednutí na místě, které jim vyhovuje.</span></p>', NULL, 3, 1, 0, 1, 0, 1, 1, 2, '{"shop":"14:00","supplier_store":"14:00"}', 1, '{"1":"standard"}', '{"deliveryPayment":[{"icon":["527112"]}]}');

-- DPD delivery method currencies
INSERT INTO `delivery_method_currency` (`id`, `deliveryMethodId`, `currency`) VALUES
	(51, 11, 'CZK');
INSERT INTO `delivery_method_currency` (`id`, `deliveryMethodId`, `currency`) VALUES
	(52, 11, 'EUR');
INSERT INTO `delivery_method_currency` (`id`, `deliveryMethodId`, `currency`) VALUES
	(53, 12, 'CZK');
INSERT INTO `delivery_method_currency` (`id`, `deliveryMethodId`, `currency`) VALUES
	(54, 12, 'EUR');

-- DPD delivery method prices
INSERT INTO `delivery_method_price` (`id`, `externalId`, `deliveryMethodId`, `priceLevelId`, `stateId`, `price_amount`, `price_currency`, `freeFrom`, `maxWeight`, `maxCodPrice`) VALUES
	(71, NULL, 11, 1, 1, 119.0000, 'CZK', 1299.0000, 31500, 50000);
INSERT INTO `delivery_method_price` (`id`, `externalId`, `deliveryMethodId`, `priceLevelId`, `stateId`, `price_amount`, `price_currency`, `freeFrom`, `maxWeight`, `maxCodPrice`) VALUES
	(72, NULL, 12, 1, 1, 89.0000, 'CZK', 1299.0000, 31500, 50000);

-- DPD delivery method x payment method relationships
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(11, 1); -- DPD + Bank Transfer
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(11, 2); -- DPD + Cash on Delivery
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(11, 3); -- DPD + Benefit Card
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(11, 4); -- DPD + Card Online
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(11, 7); -- DPD + Invoice Payment
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(11, 8); -- DPD + Pluxee Card
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(11, 9); -- DPD + Edenred Card

INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(12, 1); -- DPD Pickup + Bank Transfer
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(12, 2); -- DPD Pickup + Cash on Delivery
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(12, 3); -- DPD Pickup + Benefit Card
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(12, 4); -- DPD Pickup + Card Online
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(12, 7); -- DPD Pickup + Invoice Payment
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(12, 8); -- DPD Pickup + Pluxee Card
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(12, 9); -- DPD Pickup + Edenred Card

-- DPD delivery method x state relationships
INSERT INTO `delivery_method_x_state` (`deliveryMethodId`, `stateId`) VALUES
	(11, 1); -- DPD available in state 1
INSERT INTO `delivery_method_x_state` (`deliveryMethodId`, `stateId`) VALUES
	(12, 1); -- DPD Pickup available in state 1
