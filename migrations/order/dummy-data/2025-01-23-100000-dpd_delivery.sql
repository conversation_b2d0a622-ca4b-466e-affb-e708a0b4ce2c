

-- DPD delivery method
INSERT INTO `delivery_method` (`id`, `externalId`, `deliveryMethodUniqueIdentifier`, `name`, `desc`, `pageText`, `tooltip`, `sort`, `public`, `isRecommended`, `pageShow`, `isGift`, `calculateFree`, `deliveryDayFrom`, `deliveryDayTo`, `deliveryHourByStock`, `mutationId`, `vats`, `customFieldsJson`) VALUES
	(11, NULL, 'DPD', 'Kurýr DPD', '', '<p><span>Zásilky odeslané přes DPD dorazí adresátovi do druhého dne po odeslání. DPD nabízí rychlé a spolehlivé doručení přímo na adresu. O expedici objednávky Vás informuje DPD prostřednictvím e-mailové a SMS zprávy, je proto zkontrolovat správnost kontaktních <PERSON>. Stejným způsobem budete informování o doručování zásilky. Pokud zásilku odmítnete převzít, DPD ji vrací zpět na náš sklad. V košíku najdete i možnost doručení na Slovensko.</span></p>', NULL, 2, 1, 0, 1, 1, 1, 1, 2, '{"shop":"14:00","supplier_store":"14:00"}', 1, '{"1":"standard"}', '{"deliveryPayment":[{"icon":["527107"]}]}');

-- DPD delivery method currencies
INSERT INTO `delivery_method_currency` (`id`, `deliveryMethodId`, `currency`) VALUES
	(51, 11, 'CZK');
INSERT INTO `delivery_method_currency` (`id`, `deliveryMethodId`, `currency`) VALUES
	(52, 11, 'EUR');

-- DPD delivery method price
INSERT INTO `delivery_method_price` (`id`, `externalId`, `deliveryMethodId`, `priceLevelId`, `stateId`, `price_amount`, `price_currency`, `freeFrom`, `maxWeight`, `maxCodPrice`) VALUES
	(71, NULL, 11, 1, 1, 119.0000, 'CZK', 1299.0000, 31500, 50000);

-- DPD delivery method x payment method relationships
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(11, 1); -- DPD + Bank Transfer
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(11, 2); -- DPD + Cash on Delivery
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(11, 3); -- DPD + Benefit Card
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(11, 4); -- DPD + Card Online
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(11, 7); -- DPD + Invoice Payment
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(11, 8); -- DPD + Pluxee Card
INSERT INTO `delivery_method_x_payment_method` (`deliveryMethodId`, `paymentMethodId`) VALUES
	(11, 9); -- DPD + Edenred Card

-- DPD delivery method x state relationship
INSERT INTO `delivery_method_x_state` (`deliveryMethodId`, `stateId`) VALUES
	(11, 1); -- DPD available in state 1
